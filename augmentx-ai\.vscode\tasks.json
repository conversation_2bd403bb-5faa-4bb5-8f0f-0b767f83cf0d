{"version": "2.0.0", "tasks": [{"type": "npm", "script": "compile", "group": "build", "presentation": {"panel": "shared", "reveal": "silent", "clear": false}, "problemMatcher": []}, {"type": "npm", "script": "watch", "group": "build", "presentation": {"panel": "shared", "reveal": "never", "clear": false}, "isBackground": true, "problemMatcher": []}, {"type": "npm", "script": "test", "group": "test", "presentation": {"panel": "shared", "reveal": "always", "clear": true}, "problemMatcher": []}, {"label": "Package Extension", "type": "shell", "command": "vsce package", "group": "build", "presentation": {"panel": "shared", "reveal": "always", "clear": true}, "problemMatcher": []}]}