# Contributing to AugmentX AI

نرحب بمساهماتكم في تطوير AugmentX AI! هذا الدليل سيساعدكم على البدء.

We welcome contributions to AugmentX AI! This guide will help you get started.

## 🌍 Languages | اللغات

This document is available in:
- [العربية](#العربية)
- [English](#english)

---

## العربية

### 🚀 كيفية المساهمة

#### 1. إعداد البيئة التطويرية

```bash
# استنساخ المشروع
git clone https://github.com/augmentx/augmentx-ai.git
cd augmentx-ai

# تثبيت التبعيات
npm install

# تشغيل الاختبارات
npm test

# تجميع المشروع
npm run compile
```

#### 2. إنشاء فرع جديد

```bash
# إنشاء فرع للميزة الجديدة
git checkout -b feature/اسم-الميزة

# أو إنشاء فرع لإصلاح خطأ
git checkout -b fix/اسم-الإصلاح
```

#### 3. تطوير الميزة

- اتبع معايير الكود الموجودة
- أضف اختبارات للميزات الجديدة
- تأكد من أن جميع الاختبارات تمر
- اكتب تعليقات واضحة

#### 4. إرسال Pull Request

```bash
# إضافة التغييرات
git add .

# إنشاء commit
git commit -m "إضافة: وصف الميزة الجديدة"

# رفع التغييرات
git push origin feature/اسم-الميزة
```

### 📋 معايير الكود

#### JavaScript/Node.js
- استخدم ES6+ features
- اتبع نمط camelCase للمتغيرات
- استخدم const/let بدلاً من var
- أضف JSDoc للدوال المهمة

```javascript
/**
 * وصف الدالة
 * @param {string} parameter - وصف المعامل
 * @returns {Promise<Object>} وصف القيمة المرجعة
 */
async function exampleFunction(parameter) {
    // كود الدالة
}
```

#### CSS
- استخدم CSS Variables للألوان
- اتبع نمط BEM للأسماء
- دعم RTL للنصوص العربية

```css
.component__element--modifier {
    color: var(--primary-color);
    direction: rtl; /* للنصوص العربية */
}
```

### 🧪 الاختبارات

```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --testNamePattern="اسم الاختبار"

# تشغيل اختبارات التغطية
npm run test:coverage
```

### 📝 أنواع المساهمات

#### 🐛 إصلاح الأخطاء
- ابحث في Issues الموجودة أولاً
- أنشئ Issue جديد إذا لم يكن موجوداً
- اربط Pull Request بالـ Issue

#### ✨ ميزات جديدة
- ناقش الميزة في Issue أولاً
- تأكد من توافقها مع رؤية المشروع
- أضف اختبارات شاملة

#### 📚 تحسين الوثائق
- تحديث README.md
- إضافة أمثلة عملية
- ترجمة المحتوى للعربية

#### 🌐 الترجمة
- إضافة دعم لغات جديدة
- تحسين الترجمة العربية
- مراجعة النصوص الموجودة

### 🎨 معايير التصميم

#### واجهة المستخدم
- اتبع نظام الألوان الموحد
- دعم الوضع الداكن
- تصميم متجاوب
- دعم RTL للعربية

#### إمكانية الوصول
- استخدم ARIA labels
- دعم قارئ الشاشة
- تباين ألوان مناسب
- دعم التنقل بالكيبورد

### 🔍 مراجعة الكود

#### قبل إرسال PR
- [ ] جميع الاختبارات تمر
- [ ] الكود يتبع المعايير
- [ ] التوثيق محدث
- [ ] لا توجد تحذيرات

#### عملية المراجعة
1. مراجعة تلقائية (CI/CD)
2. مراجعة من المطورين
3. اختبار الميزة
4. دمج في المشروع

### 📞 التواصل

- **GitHub Issues**: للأخطاء والميزات
- **GitHub Discussions**: للنقاشات العامة
- **Email**: <EMAIL>

---

## English

### 🚀 How to Contribute

#### 1. Development Environment Setup

```bash
# Clone the repository
git clone https://github.com/augmentx/augmentx-ai.git
cd augmentx-ai

# Install dependencies
npm install

# Run tests
npm test

# Compile project
npm run compile
```

#### 2. Create a New Branch

```bash
# Create feature branch
git checkout -b feature/feature-name

# Or create bug fix branch
git checkout -b fix/fix-name
```

#### 3. Develop the Feature

- Follow existing code standards
- Add tests for new features
- Ensure all tests pass
- Write clear comments

#### 4. Submit Pull Request

```bash
# Add changes
git add .

# Create commit
git commit -m "feat: description of new feature"

# Push changes
git push origin feature/feature-name
```

### 📋 Code Standards

#### JavaScript/Node.js
- Use ES6+ features
- Follow camelCase for variables
- Use const/let instead of var
- Add JSDoc for important functions

```javascript
/**
 * Function description
 * @param {string} parameter - Parameter description
 * @returns {Promise<Object>} Return value description
 */
async function exampleFunction(parameter) {
    // Function code
}
```

#### CSS
- Use CSS Variables for colors
- Follow BEM naming convention
- Support RTL for Arabic text

```css
.component__element--modifier {
    color: var(--primary-color);
    direction: rtl; /* For Arabic text */
}
```

### 🧪 Testing

```bash
# Run all tests
npm test

# Run specific tests
npm test -- --testNamePattern="test name"

# Run coverage tests
npm run test:coverage
```

### 📝 Types of Contributions

#### 🐛 Bug Fixes
- Search existing Issues first
- Create new Issue if not found
- Link Pull Request to Issue

#### ✨ New Features
- Discuss feature in Issue first
- Ensure compatibility with project vision
- Add comprehensive tests

#### 📚 Documentation Improvements
- Update README.md
- Add practical examples
- Translate content to Arabic

#### 🌐 Translation
- Add support for new languages
- Improve Arabic translation
- Review existing text

### 🎨 Design Standards

#### User Interface
- Follow unified color system
- Support dark mode
- Responsive design
- RTL support for Arabic

#### Accessibility
- Use ARIA labels
- Support screen readers
- Appropriate color contrast
- Keyboard navigation support

### 🔍 Code Review

#### Before Submitting PR
- [ ] All tests pass
- [ ] Code follows standards
- [ ] Documentation updated
- [ ] No warnings

#### Review Process
1. Automatic review (CI/CD)
2. Developer review
3. Feature testing
4. Merge into project

### 📞 Communication

- **GitHub Issues**: For bugs and features
- **GitHub Discussions**: For general discussions
- **Email**: <EMAIL>

---

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- CHANGELOG.md for significant contributions
- GitHub contributors page

المساهمون سيتم تقديرهم في:
- قسم المساهمين في README.md
- CHANGELOG.md للمساهمات المهمة
- صفحة المساهمين في GitHub

---

## 📄 License

By contributing to AugmentX AI, you agree that your contributions will be licensed under the MIT License.

بالمساهمة في AugmentX AI، فإنك توافق على أن مساهماتك ستكون مرخصة تحت رخصة MIT.

---

Thank you for contributing to AugmentX AI! 🚀

شكراً لمساهمتكم في AugmentX AI! 🚀