{"recommendations": ["ms-vscode.vscode-typescript-next", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.test-adapter-converter", "hbenl.vscode-test-explorer", "ms-vscode.vscode-json", "redhat.vscode-yaml", "bradlc.vscode-tailwindcss", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-github-issue-notebooks", "github.vscode-pull-request-github", "ms-vscode.vscode-github-issue-notebooks"]}