/* Bug Bounty Professional Styling */

/* تنسيق رسائل Bug Bounty */
.message.assistant-message.real-scan-start,
.message.assistant-message.final-security-report {
    animation: slideInFromRight 0.5s ease-out;
}

.message.assistant-message.scan-phase {
    animation: fadeInUp 0.3s ease-out;
}

.message.assistant-message.analysis-progress {
    animation: pulse 0.5s ease-in-out;
}

/* تنسيق حاوي التحميل */
.bug-bounty-download-container {
    margin: 20px 0;
    animation: slideInFromBottom 0.6s ease-out;
}

.bug-bounty-download-container button {
    transition: all 0.3s ease;
    transform: translateY(0);
}

.bug-bounty-download-container button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
}

.bug-bounty-download-container button:active {
    transform: translateY(0);
}

/* تنسيق التقرير النهائي */
.final-security-report h2 {
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
    margin-bottom: 15px;
}

.final-security-report h3 {
    color: #e67e22;
    margin-top: 20px;
    margin-bottom: 10px;
}

.final-security-report h4 {
    color: #f1c40f;
    margin-top: 15px;
    margin-bottom: 8px;
}

.final-security-report code {
    background: rgba(0,0,0,0.4);
    padding: 3px 8px;
    border-radius: 4px;
    color: #e74c3c;
    font-family: 'Courier New', monospace;
}

.final-security-report strong {
    color: #ecf0f1;
    font-weight: bold;
}

/* تنسيق مراحل الفحص */
.scan-phase .progress-bar {
    background: linear-gradient(90deg, #3498db, #2ecc71);
    height: 8px;
    border-radius: 4px;
    transition: width 0.5s ease;
}

.scan-phase .phase-icon {
    font-size: 24px;
    margin-right: 10px;
    animation: rotate 2s linear infinite;
}

/* تنسيق رسائل التقدم */
.analysis-progress {
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.analysis-progress:hover {
    opacity: 1;
}

/* تنسيق الثغرات */
.vulnerability-item {
    background: rgba(0,0,0,0.4);
    padding: 15px;
    border-radius: 8px;
    margin: 10px 0;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.vulnerability-item:hover {
    background: rgba(0,0,0,0.6);
    transform: translateX(5px);
}

.vulnerability-critical {
    border-left-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.vulnerability-high {
    border-left-color: #e67e22;
    background: rgba(230, 126, 34, 0.1);
}

.vulnerability-medium {
    border-left-color: #f1c40f;
    background: rgba(241, 196, 15, 0.1);
}

.vulnerability-low {
    border-left-color: #2ecc71;
    background: rgba(46, 204, 113, 0.1);
}

.vulnerability-info {
    border-left-color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

/* تنسيق شارات الخطورة */
.severity-badge {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.severity-critical {
    background: #e74c3c;
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.severity-high {
    background: #e67e22;
    color: white;
    box-shadow: 0 2px 8px rgba(230, 126, 34, 0.3);
}

.severity-medium {
    background: #f1c40f;
    color: #2c3e50;
    box-shadow: 0 2px 8px rgba(241, 196, 15, 0.3);
}

.severity-low {
    background: #2ecc71;
    color: white;
    box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
}

.severity-info {
    background: #3498db;
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* الرسوم المتحركة */
@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromBottom {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes glow {
    0%, 100% {
        text-shadow: 0 0 20px currentColor;
    }
    50% {
        text-shadow: 0 0 30px currentColor, 0 0 40px currentColor;
    }
}

/* تنسيق الشبكة */
.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.security-card {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.security-card:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* تنسيق الأيقونات */
.security-icon {
    font-size: 2em;
    margin-bottom: 10px;
    display: block;
    text-align: center;
}

/* تنسيق النصوص */
.security-text {
    line-height: 1.6;
    color: #ecf0f1;
}

.security-highlight {
    background: rgba(52,152,219,0.2);
    padding: 2px 6px;
    border-radius: 4px;
    color: #3498db;
}

/* تنسيق الجداول */
.security-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.security-table th,
.security-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.security-table th {
    background: rgba(52,152,219,0.2);
    color: #3498db;
    font-weight: bold;
}

.security-table tr:hover {
    background: rgba(255,255,255,0.05);
}

/* تنسيق الأزرار */
.security-button {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52,152,219,0.3);
    margin: 5px;
}

.security-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52,152,219,0.4);
}

.security-button:active {
    transform: translateY(0);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .security-grid {
        grid-template-columns: 1fr;
    }
    
    .bug-bounty-download-container {
        margin: 15px 0;
    }
    
    .bug-bounty-download-container button {
        width: 100%;
        margin: 5px 0;
    }
    
    .final-security-report {
        padding: 15px;
    }
}
