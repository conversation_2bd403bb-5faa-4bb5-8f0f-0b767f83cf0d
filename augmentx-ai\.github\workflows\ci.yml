name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run linting
      run: npm run lint --if-present
      
    - name: Run tests
      run: npm test
      
    - name: Upload coverage to Codecov
      if: matrix.node-version == '18.x'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Compile extension
      run: npm run compile
      
    - name: Package extension
      run: |
        npm install -g vsce
        vsce package
        
    - name: Upload VSIX artifact
      uses: actions/upload-artifact@v3
      with:
        name: extension-vsix
        path: '*.vsix'

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run security audit
      run: npm audit --audit-level moderate
      
    - name: Run CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: javascript
        
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  publish:
    name: Publish to Marketplace
    runs-on: ubuntu-latest
    needs: [test, build, security]
    if: github.event_name == 'release' && github.event.action == 'published'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Compile extension
      run: npm run compile
      
    - name: Publish to VS Code Marketplace
      run: |
        npm install -g vsce
        vsce publish -p ${{ secrets.VSCE_PAT }}
      env:
        VSCE_PAT: ${{ secrets.VSCE_PAT }}
        
    - name: Publish to Open VSX Registry
      run: |
        npm install -g ovsx
        ovsx publish -p ${{ secrets.OVSX_PAT }}
      env:
        OVSX_PAT: ${{ secrets.OVSX_PAT }}

  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [test, build, security]
    if: always()
    
    steps:
    - name: Notify on success
      if: needs.test.result == 'success' && needs.build.result == 'success' && needs.security.result == 'success'
      run: |
        echo "✅ All checks passed successfully!"
        
    - name: Notify on failure
      if: needs.test.result == 'failure' || needs.build.result == 'failure' || needs.security.result == 'failure'
      run: |
        echo "❌ Some checks failed. Please review the logs."
        exit 1