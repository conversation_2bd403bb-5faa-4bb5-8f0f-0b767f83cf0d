/* AugmentX AI Styles */

:root {
    --primary-color: #0e639c;
    --primary-hover: #1177bb;
    --secondary-color: #3e3e42;
    --secondary-hover: #4e4e52;
    --background-dark: #1e1e1e;
    --background-medium: #2d2d30;
    --background-light: #3e3e42;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-color: #5e5e62;
    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--background-dark);
    color: var(--text-primary);
    line-height: 1.6;
    overflow: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-medium);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #6e6e72;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-primary);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--text-secondary);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--secondary-hover);
    color: var(--text-primary);
}

.btn-icon {
    padding: 8px;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 4px;
}

.btn-icon:hover:not(:disabled) {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-primary);
}

.btn-error {
    background: var(--error-color);
    color: var(--text-primary);
}

.btn-warning {
    background: var(--warning-color);
    color: var(--text-primary);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--background-light);
    color: var(--text-primary);
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(14, 99, 156, 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Card Styles */
.card {
    background: var(--background-medium);
    border: 1px solid var(--secondary-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--secondary-color);
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.card-body {
    color: var(--text-secondary);
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: var(--primary-color);
    color: var(--text-primary);
}

.badge-secondary {
    background: var(--secondary-color);
    color: var(--text-secondary);
}

.badge-success {
    background: var(--success-color);
    color: var(--text-primary);
}

.badge-error {
    background: var(--error-color);
    color: var(--text-primary);
}

.badge-warning {
    background: var(--warning-color);
    color: var(--text-primary);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--secondary-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loading-text {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-style: italic;
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: var(--background-dark);
    color: var(--text-primary);
    text-align: center;
    border-radius: 6px;
    padding: 5px 8px;
    font-size: 12px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    border: 1px solid var(--border-color);
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Code Highlighting */
pre {
    background: var(--background-dark);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    overflow-x: auto;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 13px;
    line-height: 1.4;
    margin: 12px 0;
}

code {
    background: var(--background-dark);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', Consolas, monospace;
    font-size: 13px;
    color: #e6db74;
}

pre code {
    background: transparent;
    padding: 0;
    color: var(--text-primary);
}

/* Syntax highlighting colors */
.language-javascript .keyword { color: #66d9ef; }
.language-javascript .string { color: #e6db74; }
.language-javascript .comment { color: #75715e; }
.language-javascript .number { color: #ae81ff; }

.language-python .keyword { color: #66d9ef; }
.language-python .string { color: #e6db74; }
.language-python .comment { color: #75715e; }
.language-python .number { color: #ae81ff; }

/* Responsive Design */
@media (max-width: 768px) {
    .btn {
        padding: 10px 14px;
        font-size: 16px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 12px;
        font-size: 16px;
    }
    
    .card {
        padding: 16px;
    }
}

/* RTL Support */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .btn {
    flex-direction: row-reverse;
}

[dir="rtl"] .form-group label {
    text-align: right;
}

[dir="rtl"] .card-header {
    flex-direction: row-reverse;
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #1177bb;
        --primary-hover: #0e639c;
    }
}

/* Focus styles for accessibility */
.btn:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }

.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }

.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-center { align-items: center; justify-content: center; }
.flex-between { justify-content: space-between; }
.flex-wrap { flex-wrap: wrap; }
.flex-1 { flex: 1; }

.hidden { display: none; }
.visible { display: block; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.rounded { border-radius: 6px; }
.rounded-full { border-radius: 50%; }

.shadow { box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); }
.shadow-lg { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4); }