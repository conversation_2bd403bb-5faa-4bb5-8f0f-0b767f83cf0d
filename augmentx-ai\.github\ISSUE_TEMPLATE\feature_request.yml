name: ✨ Feature Request | طلب ميزة
description: Suggest a new feature for AugmentX AI | اقتراح ميزة جديدة لـ AugmentX AI
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for suggesting a new feature! | شكراً لك على اقتراح ميزة جديدة!
        
        Please fill out the form below to help us understand your request. | يرجى ملء النموذج أدناه لمساعدتنا في فهم طلبك.

  - type: textarea
    id: summary
    attributes:
      label: Feature Summary | ملخص الميزة
      description: A brief summary of the feature you'd like to see | ملخص مختصر للميزة التي تود رؤيتها
      placeholder: I would like to see... | أود أن أرى...
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement | بيان المشكلة
      description: What problem does this feature solve? | ما المشكلة التي تحلها هذه الميزة؟
      placeholder: I'm frustrated when... | أشعر بالإحباط عندما...
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution | الحل المقترح
      description: Describe the solution you'd like | اوصف الحل الذي تريده
      placeholder: I would like to... | أود أن...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions | الحلول البديلة
      description: Describe any alternative solutions you've considered | اوصف أي حلول بديلة فكرت فيها
      placeholder: Alternatively, we could... | بدلاً من ذلك، يمكننا...
    validations:
      required: false

  - type: dropdown
    id: priority
    attributes:
      label: Priority | الأولوية
      description: How important is this feature to you? | ما مدى أهمية هذه الميزة بالنسبة لك؟
      options:
        - Low | منخفضة
        - Medium | متوسطة
        - High | عالية
        - Critical | حرجة
    validations:
      required: true

  - type: dropdown
    id: category
    attributes:
      label: Category | الفئة
      description: What category does this feature belong to? | إلى أي فئة تنتمي هذه الميزة؟
      options:
        - User Interface | واجهة المستخدم
        - AI Integration | تكامل الذكاء الصناعي
        - Code Analysis | تحليل الكود
        - File Management | إدارة الملفات
        - Settings | الإعدادات
        - Performance | الأداء
        - Accessibility | إمكانية الوصول
        - Localization | الترجمة
        - Other | أخرى
    validations:
      required: true

  - type: textarea
    id: use-cases
    attributes:
      label: Use Cases | حالات الاستخدام
      description: Describe specific use cases for this feature | اوصف حالات استخدام محددة لهذه الميزة
      placeholder: |
        1. As a developer, I want to... | كمطور، أريد أن...
        2. When working on large projects... | عند العمل على مشاريع كبيرة...
    validations:
      required: false

  - type: textarea
    id: mockups
    attributes:
      label: Mockups/Examples | نماذج/أمثلة
      description: If applicable, add mockups, screenshots, or examples | إذا كان ذلك مناسباً، أضف نماذج أو لقطات شاشة أو أمثلة
    validations:
      required: false

  - type: textarea
    id: technical
    attributes:
      label: Technical Considerations | الاعتبارات التقنية
      description: Any technical considerations or constraints | أي اعتبارات أو قيود تقنية
    validations:
      required: false

  - type: checkboxes
    id: implementation
    attributes:
      label: Implementation | التنفيذ
      description: Are you willing to help implement this feature? | هل أنت مستعد للمساعدة في تنفيذ هذه الميزة؟
      options:
        - label: I would like to implement this feature myself | أود تنفيذ هذه الميزة بنفسي
        - label: I can help with testing | يمكنني المساعدة في الاختبار
        - label: I can help with documentation | يمكنني المساعدة في التوثيق
        - label: I can provide feedback during development | يمكنني تقديم ملاحظات أثناء التطوير

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist | قائمة التحقق
      description: Please confirm the following | يرجى تأكيد ما يلي
      options:
        - label: I have searched for existing feature requests | لقد بحثت عن طلبات الميزات الموجودة
          required: true
        - label: This feature aligns with the project's goals | هذه الميزة تتماشى مع أهداف المشروع
          required: true
        - label: I have provided sufficient detail | لقد قدمت تفاصيل كافية
          required: true