{"editor.insertSpaces": true, "editor.tabSize": 4, "editor.detectIndentation": false, "files.exclude": {"out": false, "node_modules": true, "coverage": true, ".nyc_output": true}, "search.exclude": {"out": true, "node_modules": true, "coverage": true, ".nyc_output": true}, "typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "typescript"], "files.associations": {"*.json": "jsonc"}, "json.schemas": [{"fileMatch": ["package.json"], "url": "https://json.schemastore.org/package.json"}], "emmet.includeLanguages": {"javascript": "javascriptreact"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}