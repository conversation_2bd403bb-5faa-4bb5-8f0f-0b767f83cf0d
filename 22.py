import os

def split_code_file(input_file, lines_per_file=1000, max_files=None, output_folder="splits"):
    if not os.path.exists(input_file):
        print(f"❌ الملف غير موجود: {input_file}")
        return

    with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()

    total_lines = len(lines)
    os.makedirs(output_folder, exist_ok=True)

    print(f"📄 إجمالي عدد الأسطر: {total_lines}")
    file_count = 0
    for i in range(0, total_lines, lines_per_file):
        if max_files and file_count >= max_files:
            print(f"🛑 تم الوصول إلى الحد الأقصى للملفات: {max_files}")
            break

        chunk = lines[i:i + lines_per_file]
        file_count += 1
        output_path = os.path.join(output_folder, f"part_{file_count}.txt")
        with open(output_path, 'w', encoding='utf-8') as out:
            out.writelines(chunk)
        print(f"✅ تم إنشاء الملف: {output_path} ({len(chunk)} سطر)")

    print(f"\n📦 تم إنشاء {file_count} ملف داخل مجلد: {output_folder}")

# 🔁 تعديل الإعدادات هنا
split_code_file(
    input_file="1.js",        # ضع اسم الملف الكامل هنا
    lines_per_file=4000,           # عدد الأسطر في كل ملف
    max_files=None,                # أو حدد مثلاً 10 للحد الأقصى (أو اجعلها None)
    output_folder="code_parts"     # اسم مجلد الإخراج
)