module.exports = {
    // Test environment
    testEnvironment: 'node',
    
    // Test file patterns
    testMatch: [
        '**/test/**/*.test.js',
        '**/test/**/*.spec.js'
    ],
    
    // Coverage settings
    collectCoverage: false,
    coverageDirectory: 'coverage',
    coverageReporters: ['text'],
    collectCoverageFrom: [
        'src/**/*.js',
        '!src/**/*.test.js',
        '!src/**/*.spec.js'
    ],
    
    // Setup files
    setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
    
    // Module paths
    moduleDirectories: ['node_modules', 'src'],
    
    // Transform files (disabled for now)
    transform: {},
    
    // Mock VS Code API
    moduleNameMapper: {
        '^vscode$': '<rootDir>/test/mocks/vscode.js'
    },
    
    // Test timeout
    testTimeout: 10000,
    
    // Verbose output
    verbose: true,
    
    // Clear mocks between tests
    clearMocks: true,
    
    // Restore mocks after each test
    restoreMocks: true
};