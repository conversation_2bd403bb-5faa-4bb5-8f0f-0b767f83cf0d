name: 🐛 Bug Report | تقرير خطأ
description: Report a bug in AugmentX AI | الإبلاغ عن خطأ في AugmentX AI
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to report a bug! | شكراً لك على الوقت المخصص للإبلاغ عن الخطأ!
        
        Please fill out the form below to help us understand and fix the issue. | يرجى ملء النموذج أدناه لمساعدتنا في فهم وإصلاح المشكلة.

  - type: textarea
    id: description
    attributes:
      label: Bug Description | وصف الخطأ
      description: A clear and concise description of what the bug is. | وصف واضح ومختصر للخطأ.
      placeholder: Describe the bug... | اوصف الخطأ...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce | خطوات إعادة الإنتاج
      description: Steps to reproduce the behavior | خطوات إعادة إنتاج السلوك
      placeholder: |
        1. Go to... | اذهب إلى...
        2. Click on... | اضغط على...
        3. See error | شاهد الخطأ
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior | السلوك المتوقع
      description: What you expected to happen | ما كنت تتوقع حدوثه
      placeholder: I expected... | كنت أتوقع...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior | السلوك الفعلي
      description: What actually happened | ما حدث فعلياً
      placeholder: Instead, this happened... | بدلاً من ذلك، حدث هذا...
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System | نظام التشغيل
      description: What operating system are you using? | ما نظام التشغيل الذي تستخدمه؟
      options:
        - Windows
        - macOS
        - Linux (Ubuntu)
        - Linux (Other)
        - Other
    validations:
      required: true

  - type: input
    id: vscode-version
    attributes:
      label: VS Code Version | إصدار VS Code
      description: What version of VS Code are you using? | ما إصدار VS Code الذي تستخدمه؟
      placeholder: e.g., 1.85.0
    validations:
      required: true

  - type: input
    id: extension-version
    attributes:
      label: Extension Version | إصدار الإضافة
      description: What version of AugmentX AI are you using? | ما إصدار AugmentX AI الذي تستخدمه؟
      placeholder: e.g., 1.0.0
    validations:
      required: true

  - type: dropdown
    id: provider
    attributes:
      label: AI Provider | مزود الذكاء الصناعي
      description: Which AI provider are you using? | أي مزود ذكاء صناعي تستخدم؟
      options:
        - LM Studio
        - OpenRouter
        - HuggingFace
        - Other
    validations:
      required: false

  - type: textarea
    id: logs
    attributes:
      label: Error Logs | سجلات الأخطاء
      description: Please paste any relevant error logs or console output | يرجى لصق أي سجلات أخطاء أو مخرجات وحدة التحكم ذات الصلة
      render: shell
    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: Additional Context | سياق إضافي
      description: Add any other context about the problem here | أضف أي سياق آخر حول المشكلة هنا
    validations:
      required: false

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist | قائمة التحقق
      description: Please confirm the following | يرجى تأكيد ما يلي
      options:
        - label: I have searched for existing issues | لقد بحثت عن المشاكل الموجودة
          required: true
        - label: I have provided all required information | لقد قدمت جميع المعلومات المطلوبة
          required: true
        - label: I am using the latest version of the extension | أستخدم أحدث إصدار من الإضافة
          required: false