# AugmentX AI - إضافة الذكاء الصناعي لـ Visual Studio Code

<div align="center">

![AugmentX AI Logo](media/icon.png)

**إضافة احترافية للبرمجة بالذكاء الصناعي مع دعم كامل للغة العربية**

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/augmentx/augmentx-ai)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Arabic Support](https://img.shields.io/badge/Arabic-مدعوم-success.svg)]()

[English](#english) | [العربية](#arabic)

</div>

---

## العربية {#arabic}

### 🚀 نظرة عامة

**AugmentX AI** هي إضافة احترافية لبرنامج Visual Studio Code تجمع بين قوة الذكاء الصناعي وسهولة الاستخدام. تدعم الإضافة اللغة العربية بالكامل وتوفر وضعين للعمل: الوضع الذكي التلقائي ووضع الدردشة.

### ✨ المميزات الرئيسية

#### 🤖 الوضع الذكي التلقائي (Agent Auto Mode)
- **تحليل تلقائي للكود**: يحلل الكود المفتوح تلقائياً ويقدم اقتراحات
- **إصلاح الأخطاء**: يكتشف ويصحح الأخطاء البرمجية تلقائياً
- **إنشاء ملفات جديدة**: ينشئ ملفات Python, HTML, JavaScript وغيرها حسب الطلب
- **تحليل المشروع الكامل**: يحلل المشروع بأكمله ويستنتج المشاكل
- **تنفيذ تلقائي**: ينفذ المهام والتعديلات تلقائياً

#### 💬 وضع الدردشة (Chat Mode)
- **دردشة ذكية**: محادثة نصية مع النموذج بدون تعديل تلقائي
- **استشارات برمجية**: يقدم إجابات واقتراحات حول الكود والمشاكل
- **شرح الكود**: يشرح الكود المعقد بطريقة مبسطة
- **أفضل الممارسات**: ينصح بأفضل الممارسات في البرمجة

#### ⚙️ إعدادات متقدمة
- **مزودي خدمة متعددين**: دعم LM Studio, OpenRouter, HuggingFace
- **نماذج متنوعة**: دعم GPT-4, DeepSeek Coder, CodeQwen وغيرها
- **تخصيص كامل**: إعدادات مرنة لكل مزود خدمة
- **حفظ تلقائي**: حفظ الإعدادات محلياً

#### 📁 إدارة الملفات والجلسات
- **رفع الملفات**: رفع وتحليل الملفات مباشرة
- **حفظ الجلسات**: حفظ واسترجاع سجل المحادثات
- **تصفح التاريخ**: تصفح الجلسات السابقة
- **تصدير واستيراد**: تصدير واستيراد الجلسات

#### 🎨 واجهة مستخدم احترافية
- **الوضع الداكن**: تصميم عصري يريح العين
- **دعم RTL**: دعم كامل لاتجاه النص من اليمين لليسار
- **واجهة تفاعلية**: أزرار واضحة وتنقل سهل
- **تصميم متجاوب**: يتكيف مع أحجام الشاشات المختلفة

### 📦 التثبيت

#### الطريقة الأولى: من VS Code Marketplace
```bash
# ابحث عن "AugmentX AI" في متجر الإضافات
```

#### الطريقة الثانية: التثبيت اليدوي
```bash
# استنساخ المشروع
git clone https://github.com/augmentx/augmentx-ai.git
cd augmentx-ai

# تثبيت التبعيات
npm install

# تجميع الإضافة
npm run compile

# تثبيت الإضافة في VS Code
code --install-extension augmentx-ai-1.0.0.vsix
```

### 🔧 الإعداد السريع

1. **افتح VS Code** وابحث عن أيقونة الروبوت في الشريط الجانبي
2. **اضغط على أيقونة الإعدادات** ⚙️
3. **اختر مزود الخدمة**:
   - **LM Studio**: للاستخدام المحلي (مجاني)
   - **OpenRouter**: للوصول لنماذج متقدمة
   - **HuggingFace**: للنماذج مفتوحة المصدر

4. **أدخل إعدادات API**:
   ```
   LM Studio: http://localhost:1234/v1
   OpenRouter: https://openrouter.ai/api/v1
   HuggingFace: https://api-inference.huggingface.co/models
   ```

5. **اختر النموذج المفضل**:
   - `deepseek-coder` (موصى به للبرمجة)
   - `gpt-4` (للمهام المتقدمة)
   - `codeqwen` (للكود متعدد اللغات)

6. **احفظ الإعدادات** وابدأ الاستخدام!

### 🎯 كيفية الاستخدام

#### البدء السريع
```bash
# افتح لوحة الأوامر
Ctrl+Shift+P (Windows/Linux) أو Cmd+Shift+P (Mac)

# اكتب أحد الأوامر التالية:
AugmentX: Start AugmentX AI
AugmentX: Switch to Agent Auto Mode
AugmentX: Switch to Chat Mode
AugmentX: Open Settings
```

#### أمثلة عملية

**في الوضع الذكي التلقائي:**
```
"أنشئ تطبيق ويب بسيط باستخدام HTML و CSS و JavaScript"
"حلل هذا الكود واكتشف الأخطاء"
"أضف تعليقات توضيحية لهذا الكود"
"حسن أداء هذه الدالة"
```

**في وضع الدردشة:**
```
"ما هي أفضل طريقة لتحسين أداء قاعدة البيانات؟"
"اشرح لي مفهوم البرمجة الكائنية"
"ما الفرق بين React و Vue.js؟"
"كيف أتعامل مع الأخطاء في JavaScript؟"
```

### 🛠️ الأوامر المتاحة

| الأمر | الوصف | الاختصار |
|-------|--------|-----------|
| `augmentx-ai.start` | تشغيل الإضافة | `Ctrl+Alt+A` |
| `augmentx-ai.openSettings` | فتح الإعدادات | `Ctrl+Alt+S` |
| `augmentx-ai.switchToAuto` | التبديل للوضع الذكي | `Ctrl+Alt+1` |
| `augmentx-ai.switchToChat` | التبديل لوضع الدردشة | `Ctrl+Alt+2` |
| `augmentx-ai.newSession` | جلسة جديدة | `Ctrl+Alt+N` |

### 🔐 الأمان والخصوصية

- **لا توجد قواعد بيانات خارجية**: كل البيانات محفوظة محلياً
- **تشفير الإعدادات**: مفاتيح API محفوظة بشكل آمن
- **عدم تتبع**: لا نجمع أي بيانات شخصية
- **مفتوح المصدر**: الكود متاح للمراجعة

### 🧪 الاختبار

```bash
# تشغيل الاختبارات
npm test

# اختبار التغطية
npm run test:coverage

# اختبار الأداء
npm run test:performance
```

### 🤝 المساهمة

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

```bash
# استنساخ المشروع
git clone https://github.com/augmentx/augmentx-ai.git

# إنشاء فرع جديد
git checkout -b feature/new-feature

# تطبيق التغييرات
git commit -m "Add new feature"

# رفع التغييرات
git push origin feature/new-feature
```

### 📝 التحديثات

#### الإصدار 1.0.0
- ✅ إطلاق الإصدار الأول
- ✅ دعم كامل للغة العربية
- ✅ وضعين للعمل (ذكي ودردشة)
- ✅ دعم مزودي خدمة متعددين
- ✅ واجهة مستخدم احترافية
- ✅ إدارة الجلسات والملفات

#### الإصدارات القادمة
- 🔄 دعم المزيد من النماذج
- 🔄 تحسينات الأداء
- 🔄 ميزات تعاونية
- 🔄 دعم المزيد من اللغات

### 🆘 الدعم والمساعدة

- **الوثائق**: [docs.augmentx.ai](https://docs.augmentx.ai)
- **المشاكل**: [GitHub Issues](https://github.com/augmentx/augmentx-ai/issues)
- **المناقشات**: [GitHub Discussions](https://github.com/augmentx/augmentx-ai/discussions)
- **البريد الإلكتروني**: <EMAIL>

### 📄 الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE) - راجع ملف الترخيص للتفاصيل.

---

## English {#english}

### 🚀 Overview

**AugmentX AI** is a professional Visual Studio Code extension that combines the power of artificial intelligence with ease of use. The extension fully supports Arabic language and provides two working modes: Agent Auto Mode and Chat Mode.

### ✨ Key Features

#### 🤖 Agent Auto Mode
- **Automatic code analysis**: Automatically analyzes open code and provides suggestions
- **Bug fixing**: Detects and fixes programming errors automatically
- **File creation**: Creates Python, HTML, JavaScript files and more on demand
- **Full project analysis**: Analyzes the entire project and infers problems
- **Automatic execution**: Executes tasks and modifications automatically

#### 💬 Chat Mode
- **Smart chat**: Text conversation with the model without automatic modification
- **Programming consultations**: Provides answers and suggestions about code and problems
- **Code explanation**: Explains complex code in a simplified way
- **Best practices**: Advises on programming best practices

#### ⚙️ Advanced Settings
- **Multiple providers**: Support for LM Studio, OpenRouter, HuggingFace
- **Various models**: Support for GPT-4, DeepSeek Coder, CodeQwen and more
- **Full customization**: Flexible settings for each service provider
- **Auto-save**: Save settings locally

#### 📁 File and Session Management
- **File upload**: Upload and analyze files directly
- **Session saving**: Save and retrieve conversation history
- **History browsing**: Browse previous sessions
- **Export and import**: Export and import sessions

#### 🎨 Professional User Interface
- **Dark mode**: Modern design that's easy on the eyes
- **RTL support**: Full support for right-to-left text direction
- **Interactive interface**: Clear buttons and easy navigation
- **Responsive design**: Adapts to different screen sizes

### 📦 Installation

#### Method 1: From VS Code Marketplace
```bash
# Search for "AugmentX AI" in the extensions marketplace
```

#### Method 2: Manual Installation
```bash
# Clone the project
git clone https://github.com/augmentx/augmentx-ai.git
cd augmentx-ai

# Install dependencies
npm install

# Compile the extension
npm run compile

# Install extension in VS Code
code --install-extension augmentx-ai-1.0.0.vsix
```

### 🔧 Quick Setup

1. **Open VS Code** and look for the robot icon in the sidebar
2. **Click the settings icon** ⚙️
3. **Choose service provider**:
   - **LM Studio**: For local use (free)
   - **OpenRouter**: For access to advanced models
   - **HuggingFace**: For open source models

4. **Enter API settings**:
   ```
   LM Studio: http://localhost:1234/v1
   OpenRouter: https://openrouter.ai/api/v1
   HuggingFace: https://api-inference.huggingface.co/models
   ```

5. **Choose preferred model**:
   - `deepseek-coder` (recommended for programming)
   - `gpt-4` (for advanced tasks)
   - `codeqwen` (for multilingual code)

6. **Save settings** and start using!

### 🎯 How to Use

#### Quick Start
```bash
# Open command palette
Ctrl+Shift+P (Windows/Linux) or Cmd+Shift+P (Mac)

# Type one of the following commands:
AugmentX: Start AugmentX AI
AugmentX: Switch to Agent Auto Mode
AugmentX: Switch to Chat Mode
AugmentX: Open Settings
```

#### Practical Examples

**In Agent Auto Mode:**
```
"Create a simple web app using HTML, CSS, and JavaScript"
"Analyze this code and find bugs"
"Add explanatory comments to this code"
"Optimize the performance of this function"
```

**In Chat Mode:**
```
"What's the best way to optimize database performance?"
"Explain the concept of object-oriented programming"
"What's the difference between React and Vue.js?"
"How do I handle errors in JavaScript?"
```

### 🛠️ Available Commands

| Command | Description | Shortcut |
|---------|-------------|----------|
| `augmentx-ai.start` | Start extension | `Ctrl+Alt+A` |
| `augmentx-ai.openSettings` | Open settings | `Ctrl+Alt+S` |
| `augmentx-ai.switchToAuto` | Switch to Agent mode | `Ctrl+Alt+1` |
| `augmentx-ai.switchToChat` | Switch to Chat mode | `Ctrl+Alt+2` |
| `augmentx-ai.newSession` | New session | `Ctrl+Alt+N` |

### 🔐 Security and Privacy

- **No external databases**: All data saved locally
- **Settings encryption**: API keys saved securely
- **No tracking**: We don't collect any personal data
- **Open source**: Code available for review

### 🧪 Testing

```bash
# Run tests
npm test

# Coverage testing
npm run test:coverage

# Performance testing
npm run test:performance
```

### 🤝 Contributing

We welcome your contributions! Please read the [Contributing Guide](CONTRIBUTING.md) before starting.

```bash
# Clone project
git clone https://github.com/augmentx/augmentx-ai.git

# Create new branch
git checkout -b feature/new-feature

# Apply changes
git commit -m "Add new feature"

# Push changes
git push origin feature/new-feature
```

### 📝 Updates

#### Version 1.0.0
- ✅ First release
- ✅ Full Arabic language support
- ✅ Two working modes (agent and chat)
- ✅ Multiple service provider support
- ✅ Professional user interface
- ✅ Session and file management

#### Upcoming Versions
- 🔄 Support for more models
- 🔄 Performance improvements
- 🔄 Collaborative features
- 🔄 Support for more languages

### 🆘 Support and Help

- **Documentation**: [docs.augmentx.ai](https://docs.augmentx.ai)
- **Issues**: [GitHub Issues](https://github.com/augmentx/augmentx-ai/issues)
- **Discussions**: [GitHub Discussions](https://github.com/augmentx/augmentx-ai/discussions)
- **Email**: <EMAIL>

### 📄 License

This project is licensed under the [MIT License](LICENSE) - see the license file for details.

---

<div align="center">

**صنع بـ ❤️ للمطورين العرب والعالميين**

**Made with ❤️ for Arab and global developers**

</div>