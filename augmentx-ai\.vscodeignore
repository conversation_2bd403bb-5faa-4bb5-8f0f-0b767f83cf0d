# VS Code Extension ignore file

# Source files
src/**/*.ts
src/**/*.js.map

# Test files
test/**
coverage/**
.nyc_output/**

# Build tools
webpack.config.js
rollup.config.js
gulpfile.js
Gruntfile.js

# Package managers
node_modules/**
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock
pnpm-lock.yaml

# Development files
.vscode/**
.vscode-test/**
.gitignore
.gitattributes
.editorconfig
.eslintrc*
.prettierrc*
tsconfig.json
jsconfig.json

# Documentation
docs/**
*.md
!README.md

# CI/CD
.github/**
.gitlab-ci.yml
.travis.yml
.circleci/**
azure-pipelines.yml

# Environment
.env*
.nvmrc

# IDE
.idea/**
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/**
*.log

# Runtime
pids/**
*.pid
*.seed
*.pid.lock

# Coverage
coverage/**
.nyc_output/**

# Dependency directories
jspm_packages/**

# Optional npm cache directory
.npm/**

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache/**
.parcel-cache/**

# Temporary folders
tmp/**
temp/**

# Build outputs (keep only final build)
out/test/**
dist/test/**

# Source maps (optional, comment out if needed for debugging)
**/*.js.map
**/*.ts.map

# TypeScript
*.tsbuildinfo

# Backup files
*.bak
*.backup
*~

# Local development
local/**
dev/**

# Test results
test-results/**
test-output/**

# Extension development files
*.crx
*.pem
manifest.json.backup

# Configuration files with sensitive data
config.json
secrets.json
credentials.json

# Local storage directories
localStorage/**
sessionStorage/**
settings.json
sessions/**

# Media source files (keep only optimized versions)
media/src/**
media/*.svg
!media/icon.png

# Scripts
scripts/**
tools/**

# Linting
.eslintcache
.stylelintcache

# Prettier
.prettierignore

# Babel
.babelrc*
babel.config.*

# PostCSS
postcss.config.*

# Webpack
webpack.config.*

# Rollup
rollup.config.*

# Vite
vite.config.*

# Jest
jest.config.*

# Husky
.husky/**

# Lint-staged
.lintstagedrc*

# Commitizen
.czrc

# Semantic release
.releaserc*

# Renovate
renovate.json
.renovaterc*

# Dependabot
.dependabot/**

# Security
.snyk

# Performance
.lighthouseci/**

# Storybook
.storybook/**
storybook-static/**

# Chromatic
chromatic.config.json

# Misc
*.tgz
*.tar.gz