# AugmentX AI - Project Structure | هيكل المشروع

## 📁 Directory Structure | هيكل المجلدات

```
augmentx-ai/
├── 📁 .github/                    # GitHub configuration
│   ├── 📁 ISSUE_TEMPLATE/         # Issue templates
│   │   ├── bug_report.yml         # Bug report template
│   │   └── feature_request.yml    # Feature request template
│   ├── 📁 workflows/              # GitHub Actions
│   │   └── ci.yml                 # CI/CD pipeline
│   └── pull_request_template.md   # PR template
├── 📁 .vscode/                    # VS Code configuration
│   ├── extensions.json            # Recommended extensions
│   ├── launch.json                # Debug configuration
│   ├── settings.json              # Project settings
│   └── tasks.json                 # Build tasks
├── 📁 media/                      # Media assets
│   ├── icon.png                   # Extension icon (PNG)
│   ├── icon.svg                   # Extension icon (SVG)
│   ├── icon_simple.svg            # Simple icon variant
│   └── styles.css                 # Global styles
├── 📁 scripts/                    # Development scripts
│   └── dev.js                     # Development helper script
├── 📁 src/                        # Source code
│   ├── aiService.js               # AI service integration
│   ├── extension.js               # Main extension entry point
│   ├── sessionManager.js          # Session management
│   ├── settingsManager.js         # Settings management
│   └── webviewProvider.js         # WebView provider
├── 📁 test/                       # Test files
│   ├── 📁 mocks/                  # Mock objects
│   │   └── vscode.js              # VS Code API mock
│   ├── extension.test.js          # Main test suite
│   └── setup.js                   # Test setup
├── .gitignore                     # Git ignore rules
├── .vscodeignore                  # VS Code package ignore
├── CHANGELOG.md                   # Version history
├── CONTRIBUTING.md                # Contribution guidelines
├── jest.config.js                 # Jest test configuration
├── LICENSE                        # MIT License
├── package.json                   # NPM package configuration
├── package-lock.json              # NPM lock file
├── PROJECT_STRUCTURE.md           # This file
├── README.md                      # Main documentation
├── run.sh                         # Quick start script
└── SECURITY.md                    # Security policy
```

## 🔧 Core Components | المكونات الأساسية

### 📄 Main Files | الملفات الرئيسية

| File | Description | الوصف |
|------|-------------|--------|
| `src/extension.js` | Main extension entry point | نقطة دخول الإضافة الرئيسية |
| `src/webviewProvider.js` | WebView UI provider | مزود واجهة المستخدم |
| `src/aiService.js` | AI integration service | خدمة تكامل الذكاء الصناعي |
| `src/settingsManager.js` | Settings management | إدارة الإعدادات |
| `src/sessionManager.js` | Session management | إدارة الجلسات |

### 🎨 UI Components | مكونات واجهة المستخدم

| Component | Purpose | الغرض |
|-----------|---------|--------|
| WebView | Main interface | الواجهة الرئيسية |
| Settings UI | Configuration panel | لوحة التكوين |
| Chat Interface | Conversation view | عرض المحادثة |
| File Upload | File handling | معالجة الملفات |

### 🤖 AI Integration | تكامل الذكاء الصناعي

| Provider | Support | الدعم |
|----------|---------|--------|
| LM Studio | ✅ Local AI server | خادم ذكاء صناعي محلي |
| OpenRouter | ✅ Cloud AI service | خدمة ذكاء صناعي سحابية |
| HuggingFace | ✅ Open source models | نماذج مفتوحة المصدر |

## 🌍 Internationalization | التدويل

### Supported Languages | اللغات المدعومة

- **Arabic (العربية)**: Full RTL support | دعم كامل للاتجاه من اليمين لليسار
- **English**: Complete interface | واجهة كاملة

### Language Features | ميزات اللغة

- RTL text direction for Arabic | اتجاه النص من اليمين لليسار للعربية
- Localized error messages | رسائل خطأ مترجمة
- Bilingual documentation | توثيق ثنائي اللغة
- Cultural adaptations | تكييفات ثقافية

## 🧪 Testing | الاختبار

### Test Structure | هيكل الاختبار

```
test/
├── mocks/              # Mock objects
├── extension.test.js   # Main test suite
└── setup.js           # Test configuration
```

### Test Coverage | تغطية الاختبار

- Unit tests for all core components | اختبارات وحدة لجميع المكونات الأساسية
- Integration tests | اختبارات التكامل
- Mock VS Code API | محاكاة VS Code API
- Error handling tests | اختبارات معالجة الأخطاء

## 🔐 Security | الأمان

### Security Features | ميزات الأمان

- Local data storage | تخزين البيانات محلياً
- Encrypted API keys | مفاتيح API مشفرة
- Input validation | التحقق من المدخلات
- XSS prevention | منع XSS

### Security Files | ملفات الأمان

- `SECURITY.md`: Security policy | سياسة الأمان
- Input sanitization in code | تنظيف المدخلات في الكود
- Secure API communication | تواصل API آمن

## 📦 Build System | نظام البناء

### Build Process | عملية البناء

1. **Install**: `npm install` | تثبيت التبعيات
2. **Test**: `npm test` | تشغيل الاختبارات
3. **Compile**: `npm run compile` | تجميع المشروع
4. **Package**: `vsce package` | تعبئة الإضافة

### Scripts | السكريبتات

| Script | Purpose | الغرض |
|--------|---------|--------|
| `run.sh` | Quick setup | إعداد سريع |
| `scripts/dev.js` | Development helper | مساعد التطوير |
| `npm test` | Run tests | تشغيل الاختبارات |
| `npm run compile` | Build project | بناء المشروع |

## 🚀 Deployment | النشر

### CI/CD Pipeline | خط أنابيب CI/CD

- **GitHub Actions**: Automated testing and building
- **Security scanning**: CodeQL analysis
- **Multi-platform testing**: Windows, macOS, Linux
- **Automated publishing**: VS Code Marketplace

### Release Process | عملية الإصدار

1. Version bump in `package.json`
2. Update `CHANGELOG.md`
3. Create GitHub release
4. Automated publishing to marketplace

## 📚 Documentation | التوثيق

### Documentation Files | ملفات التوثيق

| File | Content | المحتوى |
|------|---------|---------|
| `README.md` | Main documentation | التوثيق الرئيسي |
| `CONTRIBUTING.md` | Contribution guide | دليل المساهمة |
| `SECURITY.md` | Security policy | سياسة الأمان |
| `CHANGELOG.md` | Version history | تاريخ الإصدارات |

### Code Documentation | توثيق الكود

- JSDoc comments for functions | تعليقات JSDoc للدوال
- Inline code comments | تعليقات الكود المضمنة
- Type definitions | تعريفات الأنواع
- API documentation | توثيق API

## 🔄 Development Workflow | سير عمل التطوير

### Getting Started | البدء

```bash
# Clone repository | استنساخ المستودع
git clone https://github.com/augmentx/augmentx-ai.git

# Quick setup | إعداد سريع
./run.sh setup

# Or manual setup | أو إعداد يدوي
npm install
npm test
npm run compile
```

### Development Commands | أوامر التطوير

```bash
# Run tests | تشغيل الاختبارات
npm test

# Compile project | تجميع المشروع
npm run compile

# Package extension | تعبئة الإضافة
npm run package

# Clean project | تنظيف المشروع
./run.sh clean
```

### VS Code Development | تطوير VS Code

1. Open project in VS Code | افتح المشروع في VS Code
2. Press `F5` to run extension | اضغط F5 لتشغيل الإضافة
3. Test in Extension Development Host | اختبر في مضيف تطوير الإضافة
4. Debug with breakpoints | تصحيح مع نقاط التوقف

## 🤝 Contributing | المساهمة

### Contribution Areas | مجالات المساهمة

- 🐛 Bug fixes | إصلاح الأخطاء
- ✨ New features | ميزات جديدة
- 📚 Documentation | التوثيق
- 🌐 Translations | الترجمات
- 🧪 Testing | الاختبار
- 🎨 UI/UX improvements | تحسينات واجهة المستخدم

### Development Guidelines | إرشادات التطوير

- Follow existing code style | اتبع نمط الكود الموجود
- Add tests for new features | أضف اختبارات للميزات الجديدة
- Update documentation | حدث التوثيق
- Support both Arabic and English | ادعم العربية والإنجليزية

## 📞 Support | الدعم

### Getting Help | الحصول على المساعدة

- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and discussions
- **Email**: <EMAIL>
- **Documentation**: README.md and wiki

### Community | المجتمع

- **Contributors**: Welcome developers worldwide
- **Users**: Support for Arabic and English speakers
- **Feedback**: Continuous improvement based on user input

---

**Made with ❤️ for the global developer community**

**صنع بـ ❤️ للمجتمع العالمي للمطورين**