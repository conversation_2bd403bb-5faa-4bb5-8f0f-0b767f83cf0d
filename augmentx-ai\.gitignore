# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
out/
dist/
*.vsix

# VS Code settings
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# Test coverage
coverage/
.nyc_output/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/

# Grunt intermediate storage
.grunt/

# Bower dependency directory
bower_components/

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Extension specific
settings.json
sessions/
*.session

# Test files
test-results/
test-output/

# Backup files
*.bak
*.backup
*~

# Local development
local/
dev/

# Documentation build
docs/_build/
docs/build/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# C/C++
*.o
*.a
*.so
*.exe
*.dll

# Rust
target/
Cargo.lock

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out

# Ruby
*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# PHP
/vendor/
composer.phar
composer.lock
.env.php

# Database
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config.json
secrets.json
credentials.json

# IDE specific files
*.swp
*.swo
*~

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Build tools
webpack.config.js.map
rollup.config.js.map

# Monitoring and analytics
.sentryclirc

# Local storage
localStorage/
sessionStorage/

# Extension development
*.crx
*.pem
manifest.json.backup